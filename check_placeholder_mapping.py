#!/usr/bin/env python3
"""
Script để kiểm tra placeholder mapping trong workflow mới
"""

import re
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_placeholder_summary(log_text: str):
    """Phân tích placeholder summary từ log"""
    
    # Pattern để tìm placeholder summary
    pattern = r"📋 Slide (\d+) placeholder summary: ({[^}]+})"
    matches = re.findall(pattern, log_text)
    
    logger.info(f"🔍 Found {len(matches)} placeholder summaries")
    
    violations = []
    
    for slide_num, summary_str in matches:
        try:
            # Parse dictionary string
            summary_dict = eval(summary_str)
            
            logger.info(f"\n📋 Slide {slide_num}:")
            logger.info(f"   Summary: {summary_dict}")
            
            # Kiểm tra quy tắc 1:1 mapping
            title_name_count = summary_dict.get('TitleName', 0)
            title_content_count = summary_dict.get('TitleContent', 0)
            subtitle_name_count = summary_dict.get('SubtitleName', 0)
            subtitle_content_count = summary_dict.get('SubtitleContent', 0)
            
            # Check TitleName vs TitleContent
            if title_name_count > 0 and title_content_count != title_name_count:
                violation = f"Slide {slide_num}: TitleName={title_name_count} but TitleContent={title_content_count} (should be 1:1)"
                violations.append(violation)
                logger.error(f"❌ {violation}")
            
            # Check SubtitleName vs SubtitleContent  
            if subtitle_name_count > 0 and subtitle_content_count != subtitle_name_count:
                violation = f"Slide {slide_num}: SubtitleName={subtitle_name_count} but SubtitleContent={subtitle_content_count} (should be 1:1)"
                violations.append(violation)
                logger.error(f"❌ {violation}")
            
            # Check for multiple TitleContent
            if title_content_count > 1:
                violation = f"Slide {slide_num}: Multiple TitleContent ({title_content_count}) - should be only 1"
                violations.append(violation)
                logger.error(f"❌ {violation}")
            
            if not violations:
                logger.info(f"✅ Slide {slide_num}: 1:1 mapping OK")
                
        except Exception as e:
            logger.error(f"❌ Error parsing slide {slide_num} summary: {e}")
    
    # Summary
    logger.info(f"\n📊 ANALYSIS SUMMARY:")
    logger.info(f"Total slides analyzed: {len(matches)}")
    logger.info(f"Violations found: {len(violations)}")
    
    if violations:
        logger.error(f"\n❌ VIOLATIONS:")
        for violation in violations:
            logger.error(f"  - {violation}")
    else:
        logger.info(f"✅ All slides follow 1:1 mapping rule!")
    
    return violations

# Test với log data từ user
TEST_LOG_DATA = """
📋 Slide 2 placeholder summary: {'TitleName': 1, 'TitleContent': 2, 'SubtitleName': 1, 'SubtitleContent': 6} 
📋 Slide 3 placeholder summary: {'TitleName': 1, 'TitleContent': 2, 'SubtitleName': 6, 'SubtitleContent': 6}  
📋 Slide 4 placeholder summary: {'TitleName': 1, 'TitleContent': 1, 'SubtitleName': 4, 'SubtitleContent': 15}
📋 Slide 5 placeholder summary: {'TitleName': 1, 'TitleContent': 1, 'SubtitleName': 5, 'SubtitleContent': 5}
📋 Slide 6 placeholder summary: {'TitleName': 1, 'TitleContent': 4, 'SubtitleName': 5, 'SubtitleContent': 5}
📋 Slide 7 placeholder summary: {'TitleName': 1, 'TitleContent': 2, 'SubtitleName': 2, 'SubtitleContent': 8}
📋 Slide 8 placeholder summary: {'TitleName': 1, 'TitleContent': 1, 'SubtitleName': 4, 'SubtitleContent': 4}
📋 Slide 9 placeholder summary: {'TitleName': 1, 'TitleContent': 2, 'SubtitleName': 4, 'SubtitleContent': 6}
📋 Slide 10 placeholder summary: {'TitleName': 1, 'TitleContent': 1, 'SubtitleName': 4, 'SubtitleContent': 4}
📋 Slide 11 placeholder summary: {'TitleName': 1, 'TitleContent': 1, 'SubtitleName': 5, 'SubtitleContent': 5}
📋 Slide 12 placeholder summary: {'TitleName': 1, 'TitleContent': 1, 'SubtitleName': 6, 'SubtitleContent': 7}
📋 Slide 13 placeholder summary: {'TitleName': 1, 'TitleContent': 3, 'SubtitleName': 5, 'SubtitleContent': 9}
📋 Slide 14 placeholder summary: {'TitleName': 1, 'TitleContent': 2, 'SubtitleName': 7, 'SubtitleContent': 7}
📋 Slide 15 placeholder summary: {'TitleName': 1, 'TitleContent': 2, 'SubtitleName': 6, 'SubtitleContent': 6}
📋 Slide 16 placeholder summary: {'TitleName': 1, 'TitleContent': 2, 'SubtitleName': 6, 'SubtitleContent': 6}
"""

if __name__ == "__main__":
    logger.info("🔍 Analyzing placeholder mapping from user's log data...")
    violations = analyze_placeholder_summary(TEST_LOG_DATA)
    
    if violations:
        logger.info(f"\n🔧 RECOMMENDATIONS:")
        logger.info(f"1. Update placeholder prompt to enforce 1:1 mapping more strictly")
        logger.info(f"2. Add validation in parsing logic")
        logger.info(f"3. Use more explicit examples in prompt")
    else:
        logger.info(f"\n🎉 No issues found!")
