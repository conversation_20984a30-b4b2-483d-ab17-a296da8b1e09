#!/usr/bin/env python3
"""
Test script cho optimized workflow của JsonTemplateService
"""

import asyncio
import json
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Mock data cho testing
MOCK_LESSON_CONTENT = """
Cấu trúc nguyên tử và bảng tuần hoàn

Nguyên tử là đơn vị cấu tạo cơ bản của vật chất. Mỗi nguyên tử gồm hạt nhân mang điện tích dương ở trung tâm và các electron mang điện tích âm chuyển động xung quanh hạt nhân.

Hạt nhân nguyên tử được cấu tạo từ proton và neutron. Proton mang điện tích dương (+1), neutron không mang điện. Số proton trong hạt nhân xác định nguyên tố hóa học.

Electron chuyển động xung quanh hạt nhân trong các orbital. Các orbital được sắp xếp thành các lớp và phân lớp. Cấu hình electron quyết định tính chất hóa học của nguyên tố.

Bảng tuần hoàn sắp xếp các nguyên tố theo thứ tự tăng dần của số hiệu nguyên tử. Các nguyên tố trong cùng nhóm có tính chất hóa học tương tự do có cùng cấu hình electron hóa trị.
"""

MOCK_TEMPLATE_JSON = {
    "version": "1.0",
    "slideFormat": "16:9",
    "slides": [
        {
            "id": "slide_001",
            "elements": [
                {
                    "id": "title_001",
                    "text": "LessonName 100",
                    "type": "text"
                },
                {
                    "id": "desc_001", 
                    "text": "LessonDescription 200",
                    "type": "text"
                },
                {
                    "id": "date_001",
                    "text": "CreatedDate 50",
                    "type": "text"
                }
            ]
        },
        {
            "id": "slide_002",
            "elements": [
                {
                    "id": "title_002",
                    "text": "TitleName 80",
                    "type": "text"
                },
                {
                    "id": "content_002",
                    "text": "TitleContent 500",
                    "type": "text"
                }
            ]
        },
        {
            "id": "slide_003",
            "elements": [
                {
                    "id": "title_003",
                    "text": "TitleName 80",
                    "type": "text"
                },
                {
                    "id": "content_003",
                    "text": "TitleContent 400",
                    "type": "text"
                },
                {
                    "id": "subtitle_003_1",
                    "text": "SubtitleName 60",
                    "type": "text"
                },
                {
                    "id": "subcontent_003_1",
                    "text": "SubtitleContent 300",
                    "type": "text"
                },
                {
                    "id": "subtitle_003_2",
                    "text": "SubtitleName 60",
                    "type": "text"
                },
                {
                    "id": "subcontent_003_2",
                    "text": "SubtitleContent 300",
                    "type": "text"
                }
            ]
        }
    ]
}

class MockLLMService:
    """Mock LLM service cho testing"""
    
    def is_available(self):
        return True
    
    async def generate_content(self, prompt: str, max_tokens: int = 4000, temperature: float = 0.1):
        """Mock LLM response dựa trên prompt type"""
        
        if "KHUNG SLIDE tổng quát" in prompt:
            # Framework generation
            return {
                "success": True,
                "text": """
SLIDE 1: Giới thiệu về nguyên tử
Mục đích: Giới thiệu khái niệm cơ bản về nguyên tử
Nội dung chính: Định nghĩa nguyên tử và các thành phần cơ bản
---

SLIDE 2: Cấu trúc hạt nhân
Mục đích: Giải thích cấu trúc và thành phần của hạt nhân
Nội dung chính: Proton, neutron và tính chất của chúng
---

SLIDE 3: Electron và orbital
Mục đích: Mô tả electron và cách chúng sắp xếp
Nội dung chính: Orbital, lớp electron và cấu hình electron
---
"""
            }
        
        elif "Chi tiết hóa nội dung cho slide cụ thể" in prompt:
            # Detail generation
            if "SLIDE 1" in prompt or "slide 1" in prompt:
                return {
                    "success": True,
                    "text": "Nguyên tử là đơn vị cấu tạo cơ bản của vật chất. Mỗi nguyên tử có kích thước rất nhỏ, khoảng 10^-10 mét. Nguyên tử gồm hạt nhân ở trung tâm và các electron chuyển động xung quanh. Hạt nhân chiếm phần lớn khối lượng nhưng thể tích rất nhỏ so với toàn bộ nguyên tử."
                }
            elif "SLIDE 2" in prompt or "slide 2" in prompt:
                return {
                    "success": True,
                    "text": "Hạt nhân nguyên tử được cấu tạo từ hai loại hạt: proton và neutron. Proton mang điện tích dương (+1) và có khối lượng khoảng 1,67 × 10^-27 kg. Neutron không mang điện và có khối lượng gần bằng proton. Số proton trong hạt nhân quyết định nguyên tố hóa học."
                }
            else:
                return {
                    "success": True,
                    "text": "Electron là hạt mang điện tích âm (-1) chuyển động xung quanh hạt nhân. Electron được sắp xếp trong các orbital có hình dạng và mức năng lượng khác nhau. Các orbital được nhóm thành các lớp K, L, M, N... Cấu hình electron quyết định tính chất hóa học của nguyên tố."
                }
        
        elif "Gắn placeholder cho slide chi tiết" in prompt:
            # Placeholder mapping với quy tắc 1:1 mapping nghiêm ngặt
            if "slide 1" in prompt.lower():
                return {
                    "success": True,
                    "text": """Cấu trúc nguyên tử và bảng tuần hoàn #*(LessonName)*#
Bài học này giúp học sinh hiểu về cấu trúc nguyên tử và vị trí các nguyên tố trong bảng tuần hoàn #*(LessonDescription)*#
Ngày thuyết trình: 22-07-2025 #*(CreatedDate)*#"""
                }
            elif "slide 2" in prompt.lower():
                return {
                    "success": True,
                    "text": """Khái niệm nguyên tử #*(TitleName)*#
Nguyên tử là đơn vị cấu tạo cơ bản của vật chất.\\nMỗi nguyên tử có kích thước rất nhỏ, khoảng 10^-10 mét.\\nNguyên tử gồm hạt nhân ở trung tâm và các electron chuyển động xung quanh.\\nHạt nhân chiếm phần lớn khối lượng nhưng thể tích rất nhỏ so với toàn bộ nguyên tử. #*(TitleContent)*#"""
                }
            else:
                # Test case với violations để kiểm tra validation
                return {
                    "success": True,
                    "text": """Cấu trúc hạt nhân #*(TitleName)*#
Hạt nhân là trung tâm của nguyên tử. #*(TitleContent)*#
Chứa proton và neutron. #*(TitleContent)*#
Có kích thước rất nhỏ. #*(TitleContent)*#
Proton #*(SubtitleName)*#
Proton mang điện dương. #*(SubtitleContent)*#
Có khối lượng 1,67×10^-27 kg. #*(SubtitleContent)*#
Quyết định nguyên tố hóa học. #*(SubtitleContent)*#
Neutron #*(SubtitleName)*#
Neutron không mang điện. #*(SubtitleContent)*#
Có khối lượng gần bằng proton. #*(SubtitleContent)*#"""
                }
        
        return {
            "success": True,
            "text": "Mock response"
        }

class MockTextbookService:
    """Mock textbook service cho testing"""
    
    async def get_lesson_content(self, lesson_id: str):
        return {
            "lesson_content": MOCK_LESSON_CONTENT,
            "book_id": "test_book",
            "total_chunks": 1,
            "content_length": len(MOCK_LESSON_CONTENT)
        }

async def test_optimized_workflow():
    """Test optimized workflow"""
    try:
        # Import service
        from app.services.json_template_service import JsonTemplateService
        
        # Create service instance với mock services
        service = JsonTemplateService()
        service.llm_service = MockLLMService()
        service.textbook_service = MockTextbookService()
        
        logger.info("🧪 Starting optimized workflow test...")
        
        # Test workflow
        result = await service.process_json_template(
            lesson_id="test_lesson_001",
            template_json=MOCK_TEMPLATE_JSON,
            config_prompt="Tạo nội dung phù hợp với học sinh lớp 10"
        )
        
        # Kiểm tra kết quả
        logger.info("📊 Test Results:")
        logger.info(f"Success: {result.get('success', False)}")
        logger.info(f"Slides created: {result.get('slides_created', 0)}")
        
        if result.get('success'):
            processed_template = result.get('processed_template', {})
            logger.info(f"Template version: {processed_template.get('version')}")
            logger.info(f"Slide format: {processed_template.get('slideFormat')}")
            logger.info(f"Created at: {processed_template.get('createdAt')}")
            
            slides = processed_template.get('slides', [])
            for i, slide in enumerate(slides):
                logger.info(f"Slide {i+1}: {slide.get('id')} - {len(slide.get('elements', []))} elements")
        else:
            logger.error(f"❌ Test failed: {result.get('error')}")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ Test error: {e}")
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    asyncio.run(test_optimized_workflow())
