#!/usr/bin/env python3
"""
Test script để kiểm tra API thực tế với workflow mới
"""

import asyncio
import json
import logging
import aiohttp
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API endpoint
API_BASE_URL = "http://localhost:8000"
PROCESS_JSON_TEMPLATE_URL = f"{API_BASE_URL}/api/slide-generation/process-json-template"

# Test data
TEST_LESSON_ID = "test_lesson_001"
TEST_CONFIG_PROMPT = "Tạo nội dung phù hợp với học sinh lớp 10, sử dụng ngôn ngữ dễ hiểu"

TEST_TEMPLATE_JSON = {
    "version": "1.0",
    "slideFormat": "16:9",
    "slides": [
        {
            "id": "slide_001",
            "elements": [
                {
                    "id": "title_001",
                    "text": "LessonName 100",
                    "type": "text"
                },
                {
                    "id": "desc_001", 
                    "text": "LessonDescription 200",
                    "type": "text"
                },
                {
                    "id": "date_001",
                    "text": "CreatedDate 50",
                    "type": "text"
                }
            ]
        },
        {
            "id": "slide_002",
            "elements": [
                {
                    "id": "title_002",
                    "text": "TitleName 80",
                    "type": "text"
                },
                {
                    "id": "content_002",
                    "text": "TitleContent 500",
                    "type": "text"
                }
            ]
        },
        {
            "id": "slide_003",
            "elements": [
                {
                    "id": "title_003",
                    "text": "TitleName 80",
                    "type": "text"
                },
                {
                    "id": "content_003",
                    "text": "TitleContent 400",
                    "type": "text"
                },
                {
                    "id": "subtitle_003_1",
                    "text": "SubtitleName 60",
                    "type": "text"
                },
                {
                    "id": "subcontent_003_1",
                    "text": "SubtitleContent 300",
                    "type": "text"
                },
                {
                    "id": "subtitle_003_2",
                    "text": "SubtitleName 60",
                    "type": "text"
                },
                {
                    "id": "subcontent_003_2",
                    "text": "SubtitleContent 300",
                    "type": "text"
                }
            ]
        }
    ]
}

async def test_real_api():
    """Test API thực tế"""
    try:
        logger.info("🧪 Testing real API with optimized workflow...")
        
        # Prepare request data
        request_data = {
            "lesson_id": TEST_LESSON_ID,
            "template": TEST_TEMPLATE_JSON,
            "config_prompt": TEST_CONFIG_PROMPT
        }
        
        # Make API call
        async with aiohttp.ClientSession() as session:
            logger.info(f"📡 Calling API: {PROCESS_JSON_TEMPLATE_URL}")
            
            async with session.post(
                PROCESS_JSON_TEMPLATE_URL,
                json=request_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                status_code = response.status
                response_data = await response.json()
                
                logger.info(f"📊 API Response:")
                logger.info(f"Status Code: {status_code}")
                logger.info(f"Success: {response_data.get('success', False)}")
                
                if response_data.get('success'):
                    logger.info(f"Slides created: {response_data.get('slides_created', 0)}")
                    
                    processed_template = response_data.get('processed_template', {})
                    logger.info(f"Template version: {processed_template.get('version')}")
                    logger.info(f"Created at: {processed_template.get('createdAt')}")
                    
                    slides = processed_template.get('slides', [])
                    logger.info(f"📋 Slides details:")
                    
                    for i, slide in enumerate(slides):
                        slide_id = slide.get('id', 'unknown')
                        elements = slide.get('elements', [])
                        logger.info(f"  Slide {i+1}: {slide_id} - {len(elements)} elements")
                        
                        # Check for formatted content (xuống dòng)
                        for element in elements:
                            text = element.get('text', '')
                            if '\n' in text:
                                logger.info(f"    Element {element.get('id')}: Has line breaks ✅")
                                logger.info(f"    Preview: {text[:100]}...")
                    
                    # Save result to file for inspection
                    with open('api_test_result.json', 'w', encoding='utf-8') as f:
                        json.dump(response_data, f, ensure_ascii=False, indent=2)
                    logger.info("💾 Result saved to api_test_result.json")
                    
                else:
                    logger.error(f"❌ API call failed: {response_data.get('error', 'Unknown error')}")
                
                return response_data
                
    except aiohttp.ClientError as e:
        logger.error(f"❌ HTTP error: {e}")
        return {"success": False, "error": f"HTTP error: {str(e)}"}
    except Exception as e:
        logger.error(f"❌ Test error: {e}")
        return {"success": False, "error": str(e)}

async def check_api_health():
    """Kiểm tra API có hoạt động không"""
    try:
        health_url = f"{API_BASE_URL}/api/slide-generation/health"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(health_url) as response:
                if response.status == 200:
                    health_data = await response.json()
                    logger.info(f"🏥 API Health: {health_data.get('status', 'unknown')}")
                    return True
                else:
                    logger.error(f"❌ API health check failed: {response.status}")
                    return False
                    
    except Exception as e:
        logger.error(f"❌ Cannot connect to API: {e}")
        return False

if __name__ == "__main__":
    async def main():
        # Check API health first
        if await check_api_health():
            await test_real_api()
        else:
            logger.error("❌ API is not available. Please start the server first.")
    
    asyncio.run(main())
